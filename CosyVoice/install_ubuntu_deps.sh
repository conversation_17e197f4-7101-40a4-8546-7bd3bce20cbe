#!/bin/bash
# {{ AURA-X: Add - Ubuntu系统依赖安装脚本. Approval: 寸止(ID:1735286400). }}
# {{ Source: context7-mcp on 'CosyVoice Ubuntu installation' }}

set -e

echo "=== CosyVoice2-0.5B Ubuntu依赖安装脚本 ==="
echo "正在安装Ubuntu系统依赖..."

# 更新包管理器
sudo apt-get update -y

# 安装基础系统依赖
echo "安装基础系统依赖..."
sudo apt-get install -y \
    git \
    build-essential \
    curl \
    wget \
    ffmpeg \
    unzip \
    git-lfs \
    sox \
    libsox-dev \
    nvidia-cuda-toolkit \
    python3-dev \
    python3-pip \
    libasound2-dev \
    portaudio19-dev \
    libportaudio2 \
    libportaudiocpp0

# 初始化git-lfs
echo "初始化git-lfs..."
git lfs install

# 检查CUDA是否可用
echo "检查CUDA环境..."
if command -v nvcc &> /dev/null; then
    echo "CUDA已安装: $(nvcc --version | grep release)"
else
    echo "警告: 未检测到CUDA，某些GPU功能可能不可用"
fi

# 检查conda是否安装
if ! command -v conda &> /dev/null; then
    echo "错误: 未检测到conda，请先安装Miniconda或Anaconda"
    echo "下载地址: https://docs.conda.io/en/latest/miniconda.html"
    exit 1
fi

echo "创建conda环境..."
conda create -n cosyvoice -y python=3.10
echo "激活conda环境..."
source $(conda info --base)/etc/profile.d/conda.sh
conda activate cosyvoice

echo "安装pynini..."
conda install -y -c conda-forge pynini==2.1.5

echo "安装Python依赖..."
pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/ --trusted-host=mirrors.aliyun.com

# 下载CosyVoice-ttsfrd资源（如果不存在）
if [ ! -d "pretrained_models/CosyVoice-ttsfrd" ]; then
    echo "下载CosyVoice-ttsfrd资源..."
    mkdir -p pretrained_models
    git clone https://www.modelscope.cn/iic/CosyVoice-ttsfrd.git pretrained_models/CosyVoice-ttsfrd
    cd pretrained_models/CosyVoice-ttsfrd/
    if [ -f "resource.zip" ]; then
        unzip resource.zip -d .
        pip install ttsfrd-0.3.6-cp38-cp38-linux_x86_64.whl
    else
        echo "警告: resource.zip未找到，ttsfrd功能可能不可用"
    fi
    cd ../..
fi

# 设置环境变量
echo "设置环境变量..."
export PYTHONPATH="${PYTHONPATH}:$(pwd):$(pwd)/third_party/Matcha-TTS"

echo "=== 安装完成 ==="
echo "请运行以下命令激活环境:"
echo "conda activate cosyvoice"
echo "export PYTHONPATH=\"\${PYTHONPATH}:$(pwd):$(pwd)/third_party/Matcha-TTS\""
echo ""
echo "然后可以运行:"
echo "python webui.py --port 8080"
echo "或"
echo "python runtime/python/fastapi/server.py --port 50000 --model_dir pretrained_models/CosyVoice2-0.5B"
