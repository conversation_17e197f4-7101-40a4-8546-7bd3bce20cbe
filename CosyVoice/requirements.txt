# {{ AURA-X: Modify - 优化Ubuntu兼容性，添加增强功能依赖. Approval: 寸止(ID:1735286400). }}
# {{ Source: context7-mcp on 'CosyVoice2-Ex Ubuntu optimization' }}
--extra-index-url https://download.pytorch.org/whl/cu121
--extra-index-url https://pypi.nvidia.com
--trusted-host download.pytorch.org
--trusted-host pypi.nvidia.com
conformer==0.3.2
deepspeed==0.14.2; sys_platform == 'linux'
diffusers==0.29.0
funasr==1.2.0  # 添加自动识别功能支持
gdown==5.1.0
gradio==4.44.1  # 降级以提高Ubuntu兼容性
hydra-core==1.3.2
HyperPyYAML==1.2.2
inflect==7.3.1
librosa==0.10.2
lightning==2.2.4
matplotlib==3.7.5
modelscope==1.17.1  # 降级以提高稳定性
networkx==3.1
omegaconf==2.3.0
onnx==1.16.0
onnxruntime-gpu==1.18.0; sys_platform == 'linux'
onnxruntime==1.18.0
onnxconverter_common==1.14.0  # 添加ONNX转换支持
openai-whisper==20231117
protobuf==3.20.2  # 降级解决兼容性问题
pydantic==2.7.0
pyworld==0.3.4
rich==13.7.1
soundfile==0.12.1
tensorboard==2.14.0
tensorrt-cu12==10.0.1; sys_platform == 'linux'
tensorrt-cu12-bindings==10.0.1; sys_platform == 'linux'
tensorrt-cu12-libs==10.0.1; sys_platform == 'linux'
torch==2.3.1
torchaudio==2.3.1
transformers==4.40.1
uvicorn==0.30.0
wget==3.2
fastapi==0.115.6
fastapi-cli==0.0.4
WeTextProcessing==1.0.3  # 添加文本处理增强
# pynini==2.1.5 # 用conda安装 conda install -y -c conda-forge pynini==2.1.5
pyarrow==19.0.0  # 升级以支持更好的数据处理
flask  # 添加Flask API支持
flask_cors  # 添加CORS支持
ffmpeg  # 添加音频处理支持
