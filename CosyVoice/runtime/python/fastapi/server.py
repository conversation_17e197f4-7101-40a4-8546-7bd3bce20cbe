# Copyright (c) 2024 Alibaba Inc (authors: <PERSON><PERSON>)
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# {{ AURA-X: Modify - 优化FastAPI服务器，添加增强功能支持. Approval: 寸止(ID:**********). }}
# {{ Source: context7-mcp on 'CosyVoice2-Ex API optimization' }}
import os
import sys
import argparse
import logging
import time
import json
from io import BytesIO
from typing import Optional, Dict, Any
logging.getLogger('matplotlib').setLevel(logging.WARNING)
from fastapi import FastAPI, UploadFile, Form, File, HTTPException, status
from fastapi.responses import StreamingResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn
import numpy as np
ROOT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append('{}/../../..'.format(ROOT_DIR))
sys.path.append('{}/../../../third_party/Matcha-TTS'.format(ROOT_DIR))
from cosyvoice.cli.cosyvoice import CosyVoice, CosyVoice2
from cosyvoice.utils.file_utils import load_wav

# 配置日志
logger = logging.getLogger(__name__)

# 请求模型定义
class TTSRequest(BaseModel):
    text: str
    speaker: Optional[str] = None
    instruct: Optional[str] = None
    speed: Optional[float] = 1.0
    stream: Optional[bool] = False

class HealthResponse(BaseModel):
    status: str
    model_loaded: bool
    available_speakers: list
    version: str

# 创建FastAPI应用
app = FastAPI(
    title="CosyVoice2-0.5B Enhanced API",
    description="增强版CosyVoice2语音合成API，支持预训练音色、极速复刻等功能",
    version="2.0.0"
)

# 设置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"]
)

# 全局变量
cosyvoice = None
model_info = {
    "loaded": False,
    "model_type": None,
    "model_dir": None,
    "available_speakers": []
}


# 辅助函数
def generate_data(model_output):
    """生成音频数据流"""
    try:
        for i in model_output:
            tts_audio = (i['tts_speech'].numpy() * (2 ** 15)).astype(np.int16).tobytes()
            yield tts_audio
    except Exception as e:
        logger.error(f"Error generating audio data: {e}")
        raise HTTPException(status_code=500, detail=f"Audio generation failed: {str(e)}")

def validate_audio_file(file: UploadFile):
    """验证音频文件"""
    if not file.filename.lower().endswith(('.wav', '.mp3', '.flac', '.m4a')):
        raise HTTPException(status_code=400, detail="Unsupported audio format. Please use WAV, MP3, FLAC, or M4A.")
    return True

# 健康检查端点
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查接口"""
    try:
        available_speakers = []
        if cosyvoice and model_info["loaded"]:
            try:
                available_speakers = cosyvoice.list_available_spks()
            except:
                available_speakers = []

        return HealthResponse(
            status="healthy" if model_info["loaded"] else "model_not_loaded",
            model_loaded=model_info["loaded"],
            available_speakers=available_speakers,
            version="2.0.0"
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return HealthResponse(
            status="error",
            model_loaded=False,
            available_speakers=[],
            version="2.0.0"
        )

# 获取可用音色列表
@app.get("/speakers")
async def get_speakers():
    """获取可用音色列表"""
    try:
        if not cosyvoice or not model_info["loaded"]:
            raise HTTPException(status_code=503, detail="Model not loaded")

        speakers = cosyvoice.list_available_spks()
        return {"speakers": speakers, "count": len(speakers)}
    except Exception as e:
        logger.error(f"Failed to get speakers: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get speakers: {str(e)}")

# 模型信息端点
@app.get("/model/info")
async def get_model_info():
    """获取模型信息"""
    return {
        "model_loaded": model_info["loaded"],
        "model_type": model_info["model_type"],
        "model_dir": model_info["model_dir"],
        "available_speakers_count": len(model_info["available_speakers"]),
        "api_version": "2.0.0"
    }


@app.get("/inference_sft")
@app.post("/inference_sft")
async def inference_sft(tts_text: str = Form(), spk_id: str = Form()):
    model_output = cosyvoice.inference_sft(tts_text, spk_id)
    return StreamingResponse(generate_data(model_output))


@app.get("/inference_zero_shot")
@app.post("/inference_zero_shot")
async def inference_zero_shot(tts_text: str = Form(), prompt_text: str = Form(), prompt_wav: UploadFile = File()):
    prompt_speech_16k = load_wav(prompt_wav.file, 16000)
    model_output = cosyvoice.inference_zero_shot(tts_text, prompt_text, prompt_speech_16k)
    return StreamingResponse(generate_data(model_output))


@app.get("/inference_cross_lingual")
@app.post("/inference_cross_lingual")
async def inference_cross_lingual(tts_text: str = Form(), prompt_wav: UploadFile = File()):
    prompt_speech_16k = load_wav(prompt_wav.file, 16000)
    model_output = cosyvoice.inference_cross_lingual(tts_text, prompt_speech_16k)
    return StreamingResponse(generate_data(model_output))


@app.get("/inference_instruct")
@app.post("/inference_instruct")
async def inference_instruct(tts_text: str = Form(), spk_id: str = Form(), instruct_text: str = Form()):
    model_output = cosyvoice.inference_instruct(tts_text, spk_id, instruct_text)
    return StreamingResponse(generate_data(model_output))


@app.get("/inference_instruct2")
@app.post("/inference_instruct2")
async def inference_instruct2(tts_text: str = Form(), instruct_text: str = Form(), prompt_wav: UploadFile = File()):
    try:
        validate_audio_file(prompt_wav)
        prompt_speech_16k = load_wav(prompt_wav.file, 16000)
        model_output = cosyvoice.inference_instruct2(tts_text, instruct_text, prompt_speech_16k)
        return StreamingResponse(generate_data(model_output), media_type="audio/wav")
    except Exception as e:
        logger.error(f"Instruct2 inference failed: {e}")
        raise HTTPException(status_code=500, detail=f"Inference failed: {str(e)}")

# {{ AURA-X: Add - 增强功能API端点. Approval: 寸止(ID:**********). }}
# {{ Source: context7-mcp on 'CosyVoice2-Ex enhanced API endpoints' }}

# 预训练音色推理
@app.get("/inference_pretrained")
@app.post("/inference_pretrained")
async def inference_pretrained(
    tts_text: str = Form(..., description="要合成的文本"),
    speaker: str = Form(..., description="音色名称"),
    speed: float = Form(1.0, description="语速倍率"),
    stream: bool = Form(False, description="是否流式输出")
):
    """使用预训练音色进行语音合成"""
    try:
        if not cosyvoice or not model_info["loaded"]:
            raise HTTPException(status_code=503, detail="Model not loaded")

        if not hasattr(cosyvoice, 'inference_pretrained_speaker'):
            raise HTTPException(status_code=501, detail="Pretrained speaker inference not supported")

        model_output = cosyvoice.inference_pretrained_speaker(
            tts_text, speaker, stream=stream, speed=speed
        )
        return StreamingResponse(generate_data(model_output), media_type="audio/wav")
    except Exception as e:
        logger.error(f"Pretrained inference failed: {e}")
        raise HTTPException(status_code=500, detail=f"Inference failed: {str(e)}")

# 3秒极速复刻
@app.get("/inference_quick_clone")
@app.post("/inference_quick_clone")
async def inference_quick_clone(
    tts_text: str = Form(..., description="要合成的文本"),
    prompt_wav: UploadFile = File(..., description="参考音频文件"),
    prompt_text: str = Form("", description="参考音频文本（可选，为空时自动识别）"),
    speed: float = Form(1.0, description="语速倍率"),
    stream: bool = Form(False, description="是否流式输出")
):
    """3秒极速复刻功能"""
    try:
        if not cosyvoice or not model_info["loaded"]:
            raise HTTPException(status_code=503, detail="Model not loaded")

        validate_audio_file(prompt_wav)
        prompt_speech_16k = load_wav(prompt_wav.file, 16000)

        if not hasattr(cosyvoice, 'inference_quick_clone'):
            raise HTTPException(status_code=501, detail="Quick clone inference not supported")

        model_output = cosyvoice.inference_quick_clone(
            tts_text, prompt_speech_16k, prompt_text, stream=stream, speed=speed
        )
        return StreamingResponse(generate_data(model_output), media_type="audio/wav")
    except Exception as e:
        logger.error(f"Quick clone inference failed: {e}")
        raise HTTPException(status_code=500, detail=f"Inference failed: {str(e)}")

# 保存音色模型
@app.post("/save_speaker")
async def save_speaker(
    speaker_name: str = Form(..., description="音色名称"),
    prompt_wav: UploadFile = File(..., description="参考音频文件"),
    prompt_text: str = Form(..., description="参考音频文本"),
    save_path: str = Form(..., description="保存路径")
):
    """保存音色模型"""
    try:
        if not cosyvoice or not model_info["loaded"]:
            raise HTTPException(status_code=503, detail="Model not loaded")

        validate_audio_file(prompt_wav)
        prompt_speech_16k = load_wav(prompt_wav.file, 16000)

        if not hasattr(cosyvoice, 'save_speaker_model'):
            raise HTTPException(status_code=501, detail="Save speaker model not supported")

        success = cosyvoice.save_speaker_model(speaker_name, prompt_speech_16k, prompt_text, save_path)

        if success:
            return {"status": "success", "message": f"Speaker model saved to {save_path}"}
        else:
            raise HTTPException(status_code=500, detail="Failed to save speaker model")
    except Exception as e:
        logger.error(f"Save speaker failed: {e}")
        raise HTTPException(status_code=500, detail=f"Save speaker failed: {str(e)}")

# 简化的TTS接口（类似CosyVoice2-Ex）
@app.get("/tts")
@app.post("/tts")
async def simple_tts(
    text: str = Form(..., description="要合成的文本"),
    speaker: str = Form("中文女", description="音色名称"),
    instruct: str = Form("", description="指令文本（可选）"),
    speed: float = Form(1.0, description="语速倍率")
):
    """简化的TTS接口"""
    try:
        if not cosyvoice or not model_info["loaded"]:
            raise HTTPException(status_code=503, detail="Model not loaded")

        # 根据是否有指令选择不同的推理方式
        if instruct:
            model_output = cosyvoice.inference_instruct(text, speaker, instruct, speed=speed)
        else:
            model_output = cosyvoice.inference_sft(text, speaker, speed=speed)

        return StreamingResponse(generate_data(model_output), media_type="audio/wav")
    except Exception as e:
        logger.error(f"Simple TTS failed: {e}")
        raise HTTPException(status_code=500, detail=f"TTS failed: {str(e)}")


# 启动函数
def initialize_model(model_dir: str):
    """初始化模型"""
    global cosyvoice, model_info

    try:
        logger.info(f"Loading model from {model_dir}...")
        start_time = time.time()

        # 优先尝试CosyVoice2
        try:
            cosyvoice = CosyVoice2(model_dir)
            model_info["model_type"] = "CosyVoice2"
            logger.info("Successfully loaded CosyVoice2 model")
        except Exception as e:
            logger.warning(f"Failed to load CosyVoice2: {e}")
            try:
                cosyvoice = CosyVoice(model_dir)
                model_info["model_type"] = "CosyVoice"
                logger.info("Successfully loaded CosyVoice model")
            except Exception as e2:
                logger.error(f"Failed to load any model: {e2}")
                raise TypeError(f'No valid model type! CosyVoice2 error: {e}, CosyVoice error: {e2}')

        # 更新模型信息
        model_info["loaded"] = True
        model_info["model_dir"] = model_dir

        # 获取可用音色
        try:
            model_info["available_speakers"] = cosyvoice.list_available_spks()
            logger.info(f"Available speakers: {len(model_info['available_speakers'])}")
        except:
            model_info["available_speakers"] = []
            logger.warning("Failed to get available speakers")

        load_time = time.time() - start_time
        logger.info(f"Model loaded successfully in {load_time:.2f} seconds")

    except Exception as e:
        logger.error(f"Model initialization failed: {e}")
        model_info["loaded"] = False
        raise

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='CosyVoice2-0.5B Enhanced API Server')
    parser.add_argument('--port',
                        type=int,
                        default=50000,
                        help='Server port (default: 50000)')
    parser.add_argument('--host',
                        type=str,
                        default='0.0.0.0',
                        help='Server host (default: 0.0.0.0)')
    parser.add_argument('--model_dir',
                        type=str,
                        default='pretrained_models/CosyVoice2-0.5B',
                        help='Model directory path or modelscope repo id')
    parser.add_argument('--workers',
                        type=int,
                        default=1,
                        help='Number of worker processes')
    parser.add_argument('--log_level',
                        type=str,
                        default='info',
                        choices=['debug', 'info', 'warning', 'error'],
                        help='Log level')

    args = parser.parse_args()

    # 设置日志级别
    logging.getLogger().setLevel(getattr(logging, args.log_level.upper()))

    # 初始化模型
    try:
        initialize_model(args.model_dir)
        logger.info(f"Starting server on {args.host}:{args.port}")
        logger.info(f"Model type: {model_info['model_type']}")
        logger.info(f"Available speakers: {len(model_info['available_speakers'])}")
        logger.info("API documentation available at: http://{}:{}/docs".format(args.host, args.port))

        # 启动服务器
        uvicorn.run(
            app,
            host=args.host,
            port=args.port,
            workers=args.workers,
            log_level=args.log_level
        )
    except Exception as e:
        logger.error(f"Failed to start server: {e}")
        sys.exit(1)
