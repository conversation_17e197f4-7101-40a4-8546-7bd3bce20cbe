#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# {{ AURA-X: Add - Flask备选API服务器. Approval: 寸止(ID:1735286400). }}
# {{ Source: context7-mcp on 'CosyVoice2-Ex Flask API' }}

import os
import sys
import argparse
import logging
import time
import json
from io import BytesIO
from flask import Flask, request, jsonify, send_file, Response
from flask_cors import CORS
import numpy as np

# 添加路径
ROOT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append('{}/../../..'.format(ROOT_DIR))
sys.path.append('{}/../../../third_party/Matcha-TTS'.format(ROOT_DIR))

from cosyvoice.cli.cosyvoice import CosyVoice, CosyVoice2
from cosyvoice.utils.file_utils import load_wav

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
CORS(app)  # 启用CORS

# 全局变量
cosyvoice = None
model_info = {
    "loaded": False,
    "model_type": None,
    "model_dir": None,
    "available_speakers": []
}

def generate_audio_response(model_output):
    """生成音频响应"""
    try:
        audio_data = BytesIO()
        for i in model_output:
            tts_audio = (i['tts_speech'].numpy() * (2 ** 15)).astype(np.int16).tobytes()
            audio_data.write(tts_audio)
        
        audio_data.seek(0)
        return audio_data
    except Exception as e:
        logger.error(f"Error generating audio: {e}")
        raise

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查"""
    try:
        available_speakers = []
        if cosyvoice and model_info["loaded"]:
            try:
                available_speakers = cosyvoice.list_available_spks()
            except:
                available_speakers = []
        
        return jsonify({
            "status": "healthy" if model_info["loaded"] else "model_not_loaded",
            "model_loaded": model_info["loaded"],
            "available_speakers": available_speakers,
            "version": "2.0.0"
        })
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return jsonify({
            "status": "error",
            "model_loaded": False,
            "available_speakers": [],
            "version": "2.0.0"
        }), 500

@app.route('/speakers', methods=['GET'])
def get_speakers():
    """获取可用音色列表"""
    try:
        if not cosyvoice or not model_info["loaded"]:
            return jsonify({"error": "Model not loaded"}), 503
        
        speakers = cosyvoice.list_available_spks()
        return jsonify({"speakers": speakers, "count": len(speakers)})
    except Exception as e:
        logger.error(f"Failed to get speakers: {e}")
        return jsonify({"error": f"Failed to get speakers: {str(e)}"}), 500

@app.route('/model/info', methods=['GET'])
def get_model_info():
    """获取模型信息"""
    return jsonify({
        "model_loaded": model_info["loaded"],
        "model_type": model_info["model_type"],
        "model_dir": model_info["model_dir"],
        "available_speakers_count": len(model_info["available_speakers"]),
        "api_version": "2.0.0"
    })

@app.route('/tts', methods=['GET', 'POST'])
def simple_tts():
    """简化的TTS接口（兼容CosyVoice2-Ex格式）"""
    try:
        if not cosyvoice or not model_info["loaded"]:
            return jsonify({"error": "Model not loaded"}), 503
        
        # 获取参数
        if request.method == 'GET':
            text = request.args.get('text', '')
            speaker = request.args.get('speaker', '中文女')
            instruct = request.args.get('instruct', '')
            speed = float(request.args.get('speed', 1.0))
        else:
            data = request.get_json() or {}
            text = data.get('text', request.form.get('text', ''))
            speaker = data.get('speaker', request.form.get('speaker', '中文女'))
            instruct = data.get('instruct', request.form.get('instruct', ''))
            speed = float(data.get('speed', request.form.get('speed', 1.0)))
        
        if not text:
            return jsonify({"error": "Text parameter is required"}), 400
        
        # 根据是否有指令选择不同的推理方式
        if instruct:
            if hasattr(cosyvoice, 'inference_instruct'):
                model_output = cosyvoice.inference_instruct(text, speaker, instruct, speed=speed)
            else:
                model_output = cosyvoice.inference_sft(text, speaker, speed=speed)
        else:
            model_output = cosyvoice.inference_sft(text, speaker, speed=speed)
        
        # 生成音频
        audio_data = generate_audio_response(model_output)
        
        return send_file(
            audio_data,
            mimetype='audio/wav',
            as_attachment=False,
            download_name=f'tts_{int(time.time())}.wav'
        )
    except Exception as e:
        logger.error(f"TTS failed: {e}")
        return jsonify({"error": f"TTS failed: {str(e)}"}), 500

@app.route('/inference_sft', methods=['GET', 'POST'])
def inference_sft():
    """SFT推理接口"""
    try:
        if not cosyvoice or not model_info["loaded"]:
            return jsonify({"error": "Model not loaded"}), 503
        
        text = request.form.get('tts_text') or request.args.get('tts_text')
        speaker = request.form.get('spk_id') or request.args.get('spk_id')
        
        if not text or not speaker:
            return jsonify({"error": "tts_text and spk_id are required"}), 400
        
        model_output = cosyvoice.inference_sft(text, speaker)
        audio_data = generate_audio_response(model_output)
        
        return send_file(
            audio_data,
            mimetype='audio/wav',
            as_attachment=False,
            download_name=f'sft_{int(time.time())}.wav'
        )
    except Exception as e:
        logger.error(f"SFT inference failed: {e}")
        return jsonify({"error": f"SFT inference failed: {str(e)}"}), 500

@app.route('/inference_zero_shot', methods=['POST'])
def inference_zero_shot():
    """零样本推理接口"""
    try:
        if not cosyvoice or not model_info["loaded"]:
            return jsonify({"error": "Model not loaded"}), 503
        
        text = request.form.get('tts_text')
        prompt_text = request.form.get('prompt_text')
        
        if not text or not prompt_text:
            return jsonify({"error": "tts_text and prompt_text are required"}), 400
        
        if 'prompt_wav' not in request.files:
            return jsonify({"error": "prompt_wav file is required"}), 400
        
        prompt_wav = request.files['prompt_wav']
        prompt_speech_16k = load_wav(prompt_wav, 16000)
        
        model_output = cosyvoice.inference_zero_shot(text, prompt_text, prompt_speech_16k)
        audio_data = generate_audio_response(model_output)
        
        return send_file(
            audio_data,
            mimetype='audio/wav',
            as_attachment=False,
            download_name=f'zero_shot_{int(time.time())}.wav'
        )
    except Exception as e:
        logger.error(f"Zero shot inference failed: {e}")
        return jsonify({"error": f"Zero shot inference failed: {str(e)}"}), 500

def initialize_model(model_dir: str):
    """初始化模型"""
    global cosyvoice, model_info
    
    try:
        logger.info(f"Loading model from {model_dir}...")
        start_time = time.time()
        
        # 优先尝试CosyVoice2
        try:
            cosyvoice = CosyVoice2(model_dir)
            model_info["model_type"] = "CosyVoice2"
            logger.info("Successfully loaded CosyVoice2 model")
        except Exception as e:
            logger.warning(f"Failed to load CosyVoice2: {e}")
            try:
                cosyvoice = CosyVoice(model_dir)
                model_info["model_type"] = "CosyVoice"
                logger.info("Successfully loaded CosyVoice model")
            except Exception as e2:
                logger.error(f"Failed to load any model: {e2}")
                raise TypeError(f'No valid model type! CosyVoice2 error: {e}, CosyVoice error: {e2}')
        
        # 更新模型信息
        model_info["loaded"] = True
        model_info["model_dir"] = model_dir
        
        # 获取可用音色
        try:
            model_info["available_speakers"] = cosyvoice.list_available_spks()
            logger.info(f"Available speakers: {len(model_info['available_speakers'])}")
        except:
            model_info["available_speakers"] = []
            logger.warning("Failed to get available speakers")
        
        load_time = time.time() - start_time
        logger.info(f"Model loaded successfully in {load_time:.2f} seconds")
        
    except Exception as e:
        logger.error(f"Model initialization failed: {e}")
        model_info["loaded"] = False
        raise

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='CosyVoice2-0.5B Flask API Server')
    parser.add_argument('--port', type=int, default=9880, help='Server port (default: 9880)')
    parser.add_argument('--host', type=str, default='0.0.0.0', help='Server host (default: 0.0.0.0)')
    parser.add_argument('--model_dir', type=str, default='pretrained_models/CosyVoice2-0.5B',
                        help='Model directory path or modelscope repo id')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    
    args = parser.parse_args()
    
    # 初始化模型
    try:
        initialize_model(args.model_dir)
        logger.info(f"Starting Flask server on {args.host}:{args.port}")
        logger.info(f"Model type: {model_info['model_type']}")
        logger.info(f"Available speakers: {len(model_info['available_speakers'])}")
        
        # 启动服务器
        app.run(host=args.host, port=args.port, debug=args.debug, threaded=True)
    except Exception as e:
        logger.error(f"Failed to start server: {e}")
        sys.exit(1)
