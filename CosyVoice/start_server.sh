#!/bin/bash
# {{ AURA-X: Add - 服务器启动脚本. Approval: 寸止(ID:1735286400). }}
# {{ Source: context7-mcp on 'CosyVoice2-Ex server startup' }}

set -e

# 默认配置
DEFAULT_MODEL_DIR="pretrained_models/CosyVoice2-0.5B"
DEFAULT_PORT=50000
DEFAULT_API_TYPE="fastapi"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "CosyVoice2-0.5B Enhanced Server 启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -m, --model-dir DIR     模型目录路径 (默认: $DEFAULT_MODEL_DIR)"
    echo "  -p, --port PORT         服务器端口 (默认: $DEFAULT_PORT)"
    echo "  -t, --type TYPE         API类型: fastapi 或 flask (默认: $DEFAULT_API_TYPE)"
    echo "  -h, --host HOST         服务器主机 (默认: 0.0.0.0)"
    echo "  --workers NUM           工作进程数 (仅FastAPI, 默认: 1)"
    echo "  --log-level LEVEL       日志级别: debug, info, warning, error (默认: info)"
    echo "  --help                  显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                                          # 使用默认设置启动FastAPI服务器"
    echo "  $0 -t flask -p 9880                        # 启动Flask服务器在9880端口"
    echo "  $0 -m /path/to/model -p 8080 --workers 2   # 自定义模型路径和工作进程数"
    echo ""
    echo "API端点 (FastAPI):"
    echo "  http://localhost:$DEFAULT_PORT/docs         # API文档"
    echo "  http://localhost:$DEFAULT_PORT/health       # 健康检查"
    echo "  http://localhost:$DEFAULT_PORT/speakers     # 获取音色列表"
    echo "  http://localhost:$DEFAULT_PORT/tts          # 简化TTS接口"
    echo ""
    echo "API端点 (Flask):"
    echo "  http://localhost:9880/health                # 健康检查"
    echo "  http://localhost:9880/speakers              # 获取音色列表"
    echo "  http://localhost:9880/tts                   # 简化TTS接口"
}

# 检查依赖
check_dependencies() {
    print_info "检查依赖..."
    
    # 检查conda环境
    if ! command -v conda &> /dev/null; then
        print_error "未找到conda，请先安装Miniconda或Anaconda"
        exit 1
    fi
    
    # 检查是否在cosyvoice环境中
    if [[ "$CONDA_DEFAULT_ENV" != "cosyvoice" ]]; then
        print_warning "当前不在cosyvoice环境中"
        print_info "请运行: conda activate cosyvoice"
        exit 1
    fi
    
    # 检查Python包
    python -c "import torch, torchaudio, fastapi, flask" 2>/dev/null || {
        print_error "缺少必要的Python包，请运行: pip install -r requirements.txt"
        exit 1
    }
    
    print_success "依赖检查通过"
}

# 检查模型
check_model() {
    local model_dir=$1
    print_info "检查模型: $model_dir"
    
    if [[ ! -d "$model_dir" ]]; then
        print_error "模型目录不存在: $model_dir"
        print_info "请确保模型已下载到正确位置"
        exit 1
    fi
    
    # 检查关键文件
    local required_files=("cosyvoice2.yaml" "llm.pt" "flow.pt" "hift.pt")
    for file in "${required_files[@]}"; do
        if [[ ! -f "$model_dir/$file" ]]; then
            print_warning "缺少文件: $model_dir/$file"
        fi
    done
    
    print_success "模型检查完成"
}

# 设置环境变量
setup_environment() {
    print_info "设置环境变量..."
    export PYTHONPATH="${PYTHONPATH}:$(pwd):$(pwd)/third_party/Matcha-TTS"
    print_success "环境变量设置完成"
}

# 启动FastAPI服务器
start_fastapi() {
    local model_dir=$1
    local port=$2
    local host=$3
    local workers=$4
    local log_level=$5
    
    print_info "启动FastAPI服务器..."
    print_info "模型: $model_dir"
    print_info "地址: http://$host:$port"
    print_info "工作进程: $workers"
    print_info "日志级别: $log_level"
    print_info "API文档: http://$host:$port/docs"
    
    python runtime/python/fastapi/server.py \
        --model_dir "$model_dir" \
        --port "$port" \
        --host "$host" \
        --workers "$workers" \
        --log_level "$log_level"
}

# 启动Flask服务器
start_flask() {
    local model_dir=$1
    local port=$2
    local host=$3
    
    print_info "启动Flask服务器..."
    print_info "模型: $model_dir"
    print_info "地址: http://$host:$port"
    
    python runtime/python/flask/app.py \
        --model_dir "$model_dir" \
        --port "$port" \
        --host "$host"
}

# 主函数
main() {
    local model_dir="$DEFAULT_MODEL_DIR"
    local port="$DEFAULT_PORT"
    local api_type="$DEFAULT_API_TYPE"
    local host="0.0.0.0"
    local workers=1
    local log_level="info"
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -m|--model-dir)
                model_dir="$2"
                shift 2
                ;;
            -p|--port)
                port="$2"
                shift 2
                ;;
            -t|--type)
                api_type="$2"
                shift 2
                ;;
            -h|--host)
                host="$2"
                shift 2
                ;;
            --workers)
                workers="$2"
                shift 2
                ;;
            --log-level)
                log_level="$2"
                shift 2
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 验证API类型
    if [[ "$api_type" != "fastapi" && "$api_type" != "flask" ]]; then
        print_error "无效的API类型: $api_type (支持: fastapi, flask)"
        exit 1
    fi
    
    # 调整Flask默认端口
    if [[ "$api_type" == "flask" && "$port" == "$DEFAULT_PORT" ]]; then
        port=9880
    fi
    
    print_success "=== CosyVoice2-0.5B Enhanced Server ==="
    
    # 执行检查
    check_dependencies
    check_model "$model_dir"
    setup_environment
    
    # 启动服务器
    case $api_type in
        fastapi)
            start_fastapi "$model_dir" "$port" "$host" "$workers" "$log_level"
            ;;
        flask)
            start_flask "$model_dir" "$port" "$host"
            ;;
    esac
}

# 捕获中断信号
trap 'print_info "服务器已停止"; exit 0' INT TERM

# 运行主函数
main "$@"
